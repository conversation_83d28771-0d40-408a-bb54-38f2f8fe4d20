import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:nsl/models/parse_validation_entity/validate_lo_model.dart';
import 'package:nsl/models/role_model.dart';
import 'package:nsl/providers/selected_object_provider.dart';
import 'package:nsl/models/solution/go_model.dart';
import 'package:nsl/services/auth_service.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:nsl/utils/logger.dart';

import '../converters/go_text_converter.dart';
import '../models/solution/parse_validate_go.dart';
import '../services/entity_parse_validation_service.dart';
import '../services/local_objective_validation_service.dart';

enum GoDetailsStep {
  initial,
  afterValidation,
  afterLocalObjectives,
}

class GoDetailsProvider with ChangeNotifier {
  // Controllers
  final TextEditingController solutionController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController localObjectiveController =
      TextEditingController();
  final Map<int, bool> _isPathwayCreationOpen = {};
  final Map<int, bool> _isLoInsertionOpen = {};

  bool showFooter = false; // Flag to control footer visibility
  GoDetailsProvider() {
    // Add listeners to controllers to detect changes
    solutionController.addListener(_onSolutionChanged);
    descriptionController.addListener(_onDescriptionChanged);
  }

  void _onSolutionChanged() {
    _checkForChangesAndResetPublishedStatus();
  }

  void _onDescriptionChanged() {
    _checkForChangesAndResetPublishedStatus();
  }
  void toggleFooterVisibility() {
    showFooter = !showFooter;
    notifyListeners();
  }

  // State variables
  GoDetailsStep _currentStep = GoDetailsStep.initial;
  PostgresRole? _selectedRole;
  List<PostgresRole> _selectedRoles = []; // Multiple roles for agent type
  bool _isValidating = false;
  String? _validationError;

  // Validation error messages
  String? _solutionValidationError;
  String? _loValidationError;
  String? _loInsertionValidationError;

  // Mock data for demonstration - in real app this would come from API
  String? _generatedDescription;
  // List<String> _localObjectives = [];
  // Local objective details state
  bool _showLocalObjectiveDetails = false;
  int? _selectedLocalObjectiveIndex;
  set loValidationError(String? error) {
    _loValidationError = error;
    notifyListeners();
  }

  set loInsertionValidationError(String? error) {
    _loInsertionValidationError = error;
    notifyListeners();
  }

  // Method to clear specific validation error
  void clearLoValidationError() {
    _loValidationError = null;
    notifyListeners();
  }

  void clearLoInsertionValidationError() {
    _loInsertionValidationError = null;
    notifyListeners();
  }

  // Local objective details dropdown selections
  Map<int, String?> _loFunctionTypes = {}; // Track function type for each LO
  Map<int, PostgresRole?> _loSelectedRoles =
      {}; // Track selected roles for each LO
  Map<int, String?> _loExecutionRights =
      {}; // Track execution rights for each LO

  // Multiple role rows for each LO
  Map<int, List<PostgresRole?>> _loMultipleRoles =
      {}; // Track multiple roles for each LO
  Map<int, List<String?>> _loMultipleExecutionRights =
      {}; // Track multiple execution rights for each LO

  // LO-specific accordion state management
  Map<int, Map<String, dynamic>?> _loSelectedObjects =
      {}; // Track selected object for each LO
  Map<int, List<String>?> _loSelectedObjectAttributes =
      {}; // Track selected object attributes for each LO
  Map<int, List<SelectedObjectData>> _loSelectedObjectsList =
      {}; // Track multiple selected objects for each LO

  // Pathway creation state

  final Map<int, bool> _pathwayCreationStates =
      {}; // Track which LOs have pathway creation open
  Map<int, PostgresRole?> _pathwaySelectedRoles =
      {}; // Track selected roles for each LO
  Map<int, List<PostgresRole>> _pathwaySelectedMultipleRoles =
      {}; // Track multiple selected roles for each LO
  Map<int, String?> _pathwaySelectedTypes =
      {}; // Track selected types for each LO
  Map<int, String?> _pathwaySelectedLOs =
      {}; // Track selected LOs for sequential type

  // Alternative/Parallel pathway data storage - Dynamic lists
  Map<int, List<PathwayEntry>> _pathwayEntries =
      {}; // Dynamic pathway entries for each LO

  // LO insertion state
  final Map<int, bool> _loInsertionStates =
      {}; // Track which LOs have insertion text field open

  // Auto-expand input stack flag
  bool _shouldAutoExpandInputStack = false;
  final Map<int, TextEditingController> _loInsertionControllers =
      {}; // Controllers for insertion text fields

  GoModel? _currentGoModel;

  /// Getter for currentGoModel
  GoModel? get currentGoModel => _currentGoModel;

  /// Setter for currentGoModel that automatically loads data from the model
  set currentGoModel(GoModel? model) {
    _currentGoModel = model;
    if (model != null) {
      // Automatically load provider data from the GoModel when it's set
      loadProviderDataFromGoModel();
    }
  }

  /// Populates the GO details form with data from a selected GO in the left panel
  /// This method is called when user taps the "+" button on any GO in the left panel
  ///
  /// [goData] - Map containing GO information with keys:
  /// - 'name': GO name (will populate Solution field)
  /// - 'description': GO description (will populate Description field)
  /// - 'goId': GO identifier
  /// - 'localObjectives': List of String containing local objective names
  void populateFromGoSelection(Map<String, dynamic> goData) {
    // Clear any existing data
    clearAllData();

    // Set solution name (GO name)
    solutionController.text = goData['name'] ?? '';

    // Set description if available (you might want to add this to GO data structure)
    descriptionController.text = goData['description'] ?? '';

    // Create GoModel with the selected GO data
    final globalObjectives = GlobalObjectives(
      name: goData['name'] ?? '',
      description: goData['description'] ?? '',
      naturalLanguage: goData['name'] ?? '',
      roleTypes: goData['roleTypes'] ?? [], // Will be set when role is selected
      version: "1.0",
      status: "Active",
      classification: "Process",
    );

    // Create LocalObjectivesList from the GO's local objectives
    final localObjectivesList = <LocalObjectivesList>[];
    final localObjectives = goData['localObjectives'] as List<String>? ?? [];

    for (int i = 0; i < localObjectives.length; i++) {
      final loName = localObjectives[i];
      if (loName.isNotEmpty) {
        localObjectivesList.add(LocalObjectivesList(
          loNumber: i + 1,
          name: loName,
          version: "1.0",
          status: "Active",
          naturalLanguage: loName,
          goId: goData['goId'] ?? '',
          loId: "lo_${i + 1}_${DateTime.now().millisecondsSinceEpoch}",
          roleTypes: null,
          terminal: false,
          pathwayData: null,
        ));
      }
    }

    // Create and set the GoModel
    currentGoModel = GoModel(
      globalObjectives: globalObjectives,
      localObjectivesList: localObjectivesList,
    );

    // Set step to after validation to show the local objectives
    _currentStep = GoDetailsStep.afterLocalObjectives;

    Logger.info(
        'GoDetailsProvider: Populated GO data - ${goData['name']} with ${localObjectives.length} local objectives');
    notifyListeners();
  }

  // Method to clear all data
  void clearAllData() {
    solutionController.clear();
    descriptionController.clear();
    localObjectiveController.clear();
    _currentStep = GoDetailsStep.initial;
    _selectedRole = null;
    _selectedRoles.clear();
    _isValidating = false;
    _validationError = null;
    _generatedDescription = null;
    _solutionValidationError = null;
    _loValidationError = null;
    _loInsertionValidationError = null;
    _showLocalObjectiveDetails = false;
    _selectedLocalObjectiveIndex = null;

    // Clear all maps
    _loFunctionTypes.clear();
    _loSelectedRoles.clear();
    _loExecutionRights.clear();
    _loMultipleRoles.clear();
    _loMultipleExecutionRights.clear();
    _loSelectedObjects.clear();
    _loSelectedObjectAttributes.clear();
    _loSelectedObjectsList.clear();
    _pathwayCreationStates.clear();
    _pathwaySelectedRoles.clear();
    _pathwaySelectedMultipleRoles.clear();
    _pathwaySelectedTypes.clear();
    _pathwaySelectedLOs.clear();
    _pathwayEntries.clear();
    _loInsertionStates.clear();

    // Clear controllers
    for (final controller in _loInsertionControllers.values) {
      controller.dispose();
    }
    _loInsertionControllers.clear();

    currentGoModel = null;
  }

  /// Validates the solution field and shows error if empty
  bool validateSolutionField() {
    if (solutionController.text.trim().isEmpty) {
      _solutionValidationError = "Please enter solution name";
      notifyListeners();
      return false;
    } else {
      _solutionValidationError = null;
      notifyListeners();
      return true;
    }
  }

  /// Validates the local objective field and shows error if empty
  bool validateLocalObjectiveField() {
    if (localObjectiveController.text.trim().isEmpty) {
      _loValidationError =
          "Please enter local objectives seperated by full stop (.)";
      notifyListeners();
      return false;
    } else {
      _loValidationError = null;
      notifyListeners();
      return true;
    }
  }

  bool validateLocalObjectiveInsertionField() {
    if (_loInsertionControllers.values
        .any((controller) => controller.text.trim().isEmpty)) {
      _loInsertionValidationError =
          "Please enter local objectives seperated by full stop (.)";
      notifyListeners();
      return false;
    } else {
      _loInsertionValidationError = null;
      notifyListeners();
      return true;
    }
  }

  /// Clears validation errors
  void clearValidationErrors() {
    _solutionValidationError = null;
    _loValidationError = null;
    _loInsertionValidationError = null;
    notifyListeners();
  }

  void clearLocalObjectiveInsertionValidationErrors() {
    _solutionValidationError = null;
    _loInsertionValidationError = null;
    notifyListeners();
  }

  bool isGoValidateVisible = false;

  // Getters
  GoDetailsStep get currentStep => _currentStep;
  PostgresRole? get selectedRole => _selectedRole;
  List<PostgresRole> get selectedRoles => _selectedRoles;
  bool get isValidating => _isValidating;
  String? get validationError => _validationError;
  String? get generatedDescription => _generatedDescription;
  String? get solutionValidationError => _solutionValidationError;
  String? get loValidationError => _loValidationError;
  String? get loInsertionValidationError => _loInsertionValidationError;
  // List<String> get localObjectives => _localObjectives;

  // Pathway creation getters
  bool isPathwayCreationOpen(int index) {
    return _isPathwayCreationOpen[index] ?? false;
  }

  PostgresRole? getPathwaySelectedRole(int loIndex) =>
      _pathwaySelectedRoles[loIndex];
  List<PostgresRole> getPathwaySelectedMultipleRoles(int loIndex) =>
      _pathwaySelectedMultipleRoles[loIndex] ?? [];
  String? getPathwaySelectedType(int loIndex) => _pathwaySelectedTypes[loIndex];
  String? getPathwaySelectedLO(int loIndex) => _pathwaySelectedLOs[loIndex];

  // Alternative/Parallel pathway getters
  List<PathwayEntry> getPathwayEntries(int loIndex) =>
      _pathwayEntries[loIndex] ?? [];

  // Get specific entry by index
  PathwayEntry? getPathwayEntry(int loIndex, int entryIndex) {
    final entries = _pathwayEntries[loIndex];
    if (entries != null && entryIndex < entries.length) {
      return entries[entryIndex];
    }
    return null;
  }

  // Get count of pathway entries for a specific LO
  int getPathwayEntryCount(int loIndex) =>
      _pathwayEntries[loIndex]?.length ?? 0;

  // Backward compatibility getters (for existing UI)
  String? getPathwayFirstSelectedLO(int loIndex) =>
      getPathwayEntry(loIndex, 0)?.selectedLO;
  String? getPathwayFirstEntityAttribute(int loIndex) =>
      getPathwayEntry(loIndex, 0)?.entityAttribute;
  String? getPathwayFirstCondition(int loIndex) =>
      getPathwayEntry(loIndex, 0)?.condition;
  String? getPathwayFirstEntityAttributeAfterCondition(int loIndex) =>
      getPathwayEntry(loIndex, 0)?.entityAttributeAfterCondition;

  String? getPathwaySecondSelectedLO(int loIndex) =>
      getPathwayEntry(loIndex, 1)?.selectedLO;
  String? getPathwaySecondEntityAttribute(int loIndex) =>
      getPathwayEntry(loIndex, 1)?.entityAttribute;
  String? getPathwaySecondCondition(int loIndex) =>
      getPathwayEntry(loIndex, 1)?.condition;
  String? getPathwaySecondEntityAttributeAfterCondition(int loIndex) =>
      getPathwayEntry(loIndex, 1)?.entityAttributeAfterCondition;

  // LO insertion getters
  bool isLoInsertionOpen(int index) {
    return _isLoInsertionOpen[index] ?? false;
  }
  TextEditingController? getLoInsertionController(int loIndex) =>
      _loInsertionControllers[loIndex];

  // Local objective details getters
  bool get showLocalObjectiveDetails => _showLocalObjectiveDetails;
  int? get selectedLocalObjectiveIndex => _selectedLocalObjectiveIndex;

  // Local objective details dropdown getters
  String? getLoFunctionType(int loIndex) => _loFunctionTypes[loIndex];
  PostgresRole? getLoSelectedRole(int loIndex) => _loSelectedRoles[loIndex];
  String? getLoExecutionRights(int loIndex) => _loExecutionRights[loIndex];

  // Multiple roles getters
  List<PostgresRole?> getLoMultipleRoles(int loIndex) =>
      _loMultipleRoles[loIndex] ?? [];
  List<String?> getLoMultipleExecutionRights(int loIndex) =>
      _loMultipleExecutionRights[loIndex] ?? [];
  int getLoRoleRowsCount(int loIndex) => _loMultipleRoles[loIndex]?.length ?? 0;

  // LO-specific accordion state getters
  Map<String, dynamic>? getLoSelectedObject(int loIndex) =>
      _loSelectedObjects[loIndex];
  List<String>? getLoSelectedObjectAttributes(int loIndex) =>
      _loSelectedObjectAttributes[loIndex];
  List<SelectedObjectData> getLoSelectedObjectsList(int loIndex) =>
      _loSelectedObjectsList[loIndex] ?? [];
  bool hasLoSelectedObjects(int loIndex) =>
      (_loSelectedObjectsList[loIndex]?.isNotEmpty ?? false);
  int getLoSelectedObjectsCount(int loIndex) =>
      _loSelectedObjectsList[loIndex]?.length ?? 0;

  /// Get entities from GoModel's entitiesList for the specified LO
  /// This ensures all entities visible on screen come from localObjectivesList
  List<ObjectCreationModel> getLoEntitiesFromGoModel(int loIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      return [];
    }

    final lo = currentGoModel!.localObjectivesList![loIndex];
    return lo.entitiesList ?? [];
  }

  /// Check if LO has entities in GoModel's entitiesList
  bool hasLoEntitiesInGoModel(int loIndex) {
    final entities = getLoEntitiesFromGoModel(loIndex);
    return entities.isNotEmpty;
  }

  /// Get count of entities in GoModel's entitiesList for the specified LO
  int getLoEntitiesCountFromGoModel(int loIndex) {
    final entities = getLoEntitiesFromGoModel(loIndex);
    return entities.length;
  }

  // Collapse/Expand state for roles section
  final Map<int, bool> _loRolesSectionExpanded = {};

  bool isLoRolesSectionExpanded(int loIndex) =>
      _loRolesSectionExpanded[loIndex] ?? true;

  void toggleLoRolesSectionExpanded(int loIndex) {
    _loRolesSectionExpanded[loIndex] = !isLoRolesSectionExpanded(loIndex);
    notifyListeners();
  }

  final EntityParseValidationService _entityParseValidationService =
      EntityParseValidationService();

  ParseValidateGo? parseValidateGo;

  // Get available LOs for sequential selection (LOs after the current one)
  List<String> getAvailableLOsForSequential(int currentLoIndex) {
    if (currentLoIndex >= currentGoModel!.localObjectivesList!.length - 1)
      return [];
    return currentGoModel!.localObjectivesList!
        .asMap()
        .entries
        .where((entry) => entry.key > currentLoIndex)
        .map((entry) =>
            entry.value.name ?? '') // Return only the LO name without prefix
        .toList();
  }

  /// Sets the selected role
  void setSelectedRole(PostgresRole? role) {
    if (_selectedRole != role) {
      _selectedRole = role;
      _checkForChangesAndResetPublishedStatus();
      Logger.info(
          'GoDetailsProvider: Selected role changed to: ${role?.name ?? 'None'}');
      notifyListeners();
    }
  }

  /// Sets multiple selected roles for agent type
  void setSelectedRoles(List<PostgresRole> roles) {
    _selectedRoles = List.from(roles);
    _checkForChangesAndResetPublishedStatus();
    Logger.info(
        'GoDetailsProvider: Selected roles changed to: ${roles.map((r) => r.name).join(', ')}');
    notifyListeners();
  }

  /// Adds a role to the selected roles list
  void addSelectedRole(PostgresRole role) {
    if (!_selectedRoles.any((r) => r.roleId == role.roleId)) {
      _selectedRoles.add(role);
      _checkForChangesAndResetPublishedStatus();
      Logger.info('GoDetailsProvider: Added role: ${role.name}');
      notifyListeners();
    }
  }

  /// Removes a role from the selected roles list
  void removeSelectedRole(PostgresRole role) {
    _selectedRoles.removeWhere((r) => r.roleId == role.roleId);
    _checkForChangesAndResetPublishedStatus();
    Logger.info('GoDetailsProvider: Removed role: ${role.name}');
    notifyListeners();
  }

  /// Checks for changes and resets published status if GO was previously published
  void _checkForChangesAndResetPublishedStatus() {
    if (currentGoModel?.globalObjectives?.isPublished == true) {
      Logger.info(
          'GoDetailsProvider: Changes detected on published GO, resetting published and validated status');
      currentGoModel?.globalObjectives?.isPublished = false;
      currentGoModel?.globalObjectives?.isValidated = false;
      isGoValidateVisible = true;
      notifyListeners();
    }
  }

  /// Updates the solution name and checks for changes
  void updateSolutionName(String newName) {
    if (solutionController.text != newName) {
      solutionController.text = newName;
      _checkForChangesAndResetPublishedStatus();
      notifyListeners();
    }
  }

  /// Updates the solution description and checks for changes
  void updateSolutionDescription(String newDescription) {
    if (descriptionController.text != newDescription) {
      descriptionController.text = newDescription;
      _checkForChangesAndResetPublishedStatus();
      notifyListeners();
    }
  }

  /// Validates the solution and moves to next step
  Future<void> validateSolution() async {
    if (solutionController.text.trim().isEmpty) {
      return; // Just return without error, don't validate empty solution
    }

    _setValidating(true);
    _setValidationError(null);

    try {
      Logger.info(
          'GoDetailsProvider: Validating solution: ${solutionController.text}');

      // Simulate API call delay
      // await Future.delayed(const Duration(seconds: 2));

      // Mock response - in real app this would come from API
      _generateMockResponse();

      _currentStep = GoDetailsStep.afterValidation;
      Logger.info('GoDetailsProvider: Solution validated successfully');
    } catch (e) {
      Logger.error('GoDetailsProvider: Validation error - $e');
    } finally {
      _setValidating(false);
    }
  }

  /// Validates the solution with GoModel data and moves to next step
  Future<void> validateSolutionWithGoModel(GoModel? goModel) async {
    if (solutionController.text.trim().isEmpty) {
      return; // Just return without error, don't validate empty solution
    }

    _setValidating(true);
    _setValidationError(null);

    try {
      Logger.info(
          'GoDetailsProvider: Validating solution with GoModel: ${goModel?.globalObjectives?.name}');

      // Log the GoModel data being saved
      Logger.info(
          'GoDetailsProvider: Solution Name: ${goModel?.globalObjectives?.name}');
      Logger.info(
          'GoDetailsProvider: Solution Description: ${goModel?.globalObjectives?.description}');
      Logger.info(
          'GoDetailsProvider: Solution Agent Type: ${goModel?.globalObjectives?.roleTypes}');
      // Logger.info('GoDetailsProvider: Solution Description: ${goModel.globalObjectives?.description}');
      // Logger.info('GoDetailsProvider: Local Objectives Count: ${goModel.localObjectivesList?.length ?? 0}');

      // Log each LO name
      if (goModel?.localObjectivesList != null) {
        for (int i = 0; i < goModel!.localObjectivesList!.length; i++) {
          final lo = goModel.localObjectivesList![i];
          Logger.info('GoDetailsProvider: LO ${i + 1}: ${lo.name}');
        }
      }

      // // Simulate API call delay
      // await Future.delayed(const Duration(seconds: 2));

      // Mock response - in real app this would come from API
      _generateMockResponse();

      _currentStep = GoDetailsStep.afterValidation;
      Logger.info(
          'GoDetailsProvider: Solution with GoModel validated successfully');
    } catch (e) {
      Logger.error('GoDetailsProvider: Validation with GoModel error - $e');
    } finally {
      _setValidating(false);
    }
  }

  /// Generates mock response for demonstration
  void _generateMockResponse() {
    // Generate description based on solution
    _generatedDescription = ' ';
    descriptionController.text = _generatedDescription ?? '';

    // Generate mock local objectives
    // _localObjectives = [
    //   'Type LO name with full stop (.)',
    // ];

    notifyListeners();
  }

  /// Processes local objectives from text input
  void processLocalObjectives() {
    final inputText = localObjectiveController.text.trim();

    if (inputText.isEmpty) {
      Logger.warning('GoDetailsProvider: No local objectives text provided');
      return;
    }

    // Split by full stops and clean up
    final objectives = inputText
        .split('.')
        .map((obj) => obj.trim())
        .where((obj) => obj.isNotEmpty)
        .toList();

    if (objectives.isEmpty) {
      Logger.warning('GoDetailsProvider: No valid local objectives found');
      return;
    }

    // Capitalize first letter of each objective
    // _localObjectives = objectives
    //     .map((obj) => obj.isEmpty
    //         ? obj
    //         : obj[0].toUpperCase() + obj.substring(1).toLowerCase())
    //     .toList();
    currentGoModel!.localObjectivesList = objectives
        .map((obj) => LocalObjectivesList(
            name: obj.isEmpty
                ? obj
                : obj[0].toUpperCase() + obj.substring(1).toLowerCase()))
        .toList();
    _currentStep = GoDetailsStep.afterLocalObjectives;
    isGoValidateVisible = true;

    Logger.info(
        'GoDetailsProvider: Processed ${currentGoModel!.localObjectivesList!.length} local objectives: ${currentGoModel!.localObjectivesList!}');
    notifyListeners();
  }

  /// Toggles pathway creation for a specific LO
  void togglePathwayCreation(int loIndex) {
     // Close LO insertion if it's open
    _isLoInsertionOpen[loIndex] = false;
     // Toggle pathway creation
    _isPathwayCreationOpen[loIndex] = !(_isPathwayCreationOpen[loIndex] ?? false);
    _pathwayCreationStates[loIndex] =
        !(_pathwayCreationStates[loIndex] ?? false);

    // Clear selections if closing
    if (!_pathwayCreationStates[loIndex]!) {
      _pathwaySelectedRoles.remove(loIndex);
      _pathwaySelectedTypes.remove(loIndex);
      _pathwaySelectedLOs.remove(loIndex);

      // Clear alternative/parallel pathway data
      _pathwayEntries.remove(loIndex);
    }

    Logger.info(
        'GoDetailsProvider: Toggled pathway creation for LO-${loIndex + 1}: ${_pathwayCreationStates[loIndex]}');
    notifyListeners();
  }

  /// Sets the selected role for pathway creation
  void setPathwaySelectedRole(int loIndex, PostgresRole? role) {
    _pathwaySelectedRoles[loIndex] = role;

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set pathway role for LO-${loIndex + 1}: ${role?.name}');
    notifyListeners();
  }

  /// Sets multiple selected roles for pathway creation
  void setPathwaySelectedMultipleRoles(int loIndex, List<PostgresRole> roles) {
    _pathwaySelectedMultipleRoles[loIndex] = List.from(roles);

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set pathway multiple roles for LO-${loIndex + 1}: ${roles.map((r) => r.name).join(', ')}');
    notifyListeners();
  }

  /// Sets the selected type for pathway creation
  void setPathwaySelectedType(int loIndex, String? type) {
    _pathwaySelectedTypes[loIndex] = type;

    // Clear LO selection if type changes
    if (type != 'Sequential') {
      _pathwaySelectedLOs.remove(loIndex);
    }

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set pathway type for LO-${loIndex + 1}: $type');
    notifyListeners();
  }

  /// Sets the selected LO for sequential pathway
  void setPathwaySelectedLO(int loIndex, String? selectedLO) {
    _pathwaySelectedLOs[loIndex] = selectedLO;

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set pathway LO for LO-${loIndex + 1}: $selectedLO');
    notifyListeners();
  }

  // Alternative/Parallel pathway setters - Dynamic approach
  void _ensurePathwayEntryExists(int loIndex, int entryIndex) {
    if (_pathwayEntries[loIndex] == null) {
      _pathwayEntries[loIndex] = [];
    }

    // Ensure we have enough entries
    while (_pathwayEntries[loIndex]!.length <= entryIndex) {
      _pathwayEntries[loIndex]!.add(PathwayEntry());
    }
  }

  void setPathwayEntrySelectedLO(
      int loIndex, int entryIndex, String? selectedLO) {
    _ensurePathwayEntryExists(loIndex, entryIndex);
    _pathwayEntries[loIndex]![entryIndex].selectedLO = selectedLO;

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set LO for LO-${loIndex + 1}, Entry-${entryIndex + 1}: $selectedLO');
    notifyListeners();
  }

  void setPathwayEntryEntityAttribute(
      int loIndex, int entryIndex, String? attribute) {
    _ensurePathwayEntryExists(loIndex, entryIndex);
    _pathwayEntries[loIndex]![entryIndex].entityAttribute = attribute;

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set entity attribute for LO-${loIndex + 1}, Entry-${entryIndex + 1}: $attribute');
    notifyListeners();
  }

  void setPathwayEntryCondition(
      int loIndex, int entryIndex, String? condition) {
    _ensurePathwayEntryExists(loIndex, entryIndex);
    _pathwayEntries[loIndex]![entryIndex].condition = condition;

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set condition for LO-${loIndex + 1}, Entry-${entryIndex + 1}: $condition');
    notifyListeners();
  }

  void setPathwayEntryEntityAttributeAfterCondition(
      int loIndex, int entryIndex, String? attribute) {
    _ensurePathwayEntryExists(loIndex, entryIndex);
    _pathwayEntries[loIndex]![entryIndex].entityAttributeAfterCondition =
        attribute;

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set entity attribute after condition for LO-${loIndex + 1}, Entry-${entryIndex + 1}: $attribute');
    notifyListeners();
  }

  // Add new pathway entry
  void addPathwayEntry(int loIndex) {
    if (_pathwayEntries[loIndex] == null) {
      _pathwayEntries[loIndex] = [];
    }
    _pathwayEntries[loIndex]!.add(PathwayEntry());

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Added new pathway entry for LO-${loIndex + 1}. Total entries: ${_pathwayEntries[loIndex]!.length}');
    notifyListeners();
  }

  // Remove pathway entry
  void removePathwayEntry(int loIndex, int entryIndex) {
    if (_pathwayEntries[loIndex] != null &&
        entryIndex < _pathwayEntries[loIndex]!.length) {
      _pathwayEntries[loIndex]!.removeAt(entryIndex);

      // Check for changes and reset published status
      _checkForChangesAndResetPublishedStatus();

      Logger.info(
          'GoDetailsProvider: Removed pathway entry ${entryIndex + 1} for LO-${loIndex + 1}. Remaining entries: ${_pathwayEntries[loIndex]!.length}');
      notifyListeners();
    }
  }

  // Backward compatibility setters (for existing UI)
  void setPathwayFirstSelectedLO(int loIndex, String? selectedLO) {
    setPathwayEntrySelectedLO(loIndex, 0, selectedLO);
  }

  void setPathwayFirstEntityAttribute(int loIndex, String? attribute) {
    setPathwayEntryEntityAttribute(loIndex, 0, attribute);
  }

  void setPathwayFirstCondition(int loIndex, String? condition) {
    setPathwayEntryCondition(loIndex, 0, condition);
  }

  void setPathwayFirstEntityAttributeAfterCondition(
      int loIndex, String? attribute) {
    setPathwayEntryEntityAttributeAfterCondition(loIndex, 0, attribute);
  }

  void setPathwaySecondSelectedLO(int loIndex, String? selectedLO) {
    setPathwayEntrySelectedLO(loIndex, 1, selectedLO);
  }

  void setPathwaySecondEntityAttribute(int loIndex, String? attribute) {
    setPathwayEntryEntityAttribute(loIndex, 1, attribute);
  }

  void setPathwaySecondCondition(int loIndex, String? condition) {
    setPathwayEntryCondition(loIndex, 1, condition);
  }

  void setPathwaySecondEntityAttributeAfterCondition(
      int loIndex, String? attribute) {
    setPathwayEntryEntityAttributeAfterCondition(loIndex, 1, attribute);
  }

  /// Shows local objective details for a specific LO
  void setShowLocalObjectiveDetails(int loIndex) {
    _showLocalObjectiveDetails = true;
    _selectedLocalObjectiveIndex = loIndex;
    Logger.info(
        'GoDetailsProvider: Showing local objective details for LO-${loIndex + 1}');
    notifyListeners();
  }

  /// Hides local objective details
  void hideLocalObjectiveDetails() {
    _showLocalObjectiveDetails = false;
    _selectedLocalObjectiveIndex = null;
    Logger.info('GoDetailsProvider: Hiding local objective details');
    notifyListeners();
  }

  /// Sync _loSelectedObjectsList data to GoModel entitiesList before validation
  void _syncSelectedObjectsToGoModel(int loIndex) {
    Logger.info(
        'GoDetailsProvider: Syncing selected objects to GoModel for LO-${loIndex + 1}');

    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      Logger.warning(
          'GoDetailsProvider: Cannot sync - GoModel or LO not found');
      return;
    }

    final lo = currentGoModel!.localObjectivesList![loIndex];
    final selectedObjects = _loSelectedObjectsList[loIndex];

    if (selectedObjects == null || selectedObjects.isEmpty) {
      Logger.info(
          'GoDetailsProvider: No selected objects to sync for LO-${loIndex + 1}');
      return;
    }

    // Initialize entitiesList if null
    lo.entitiesList ??= [];

    Logger.info(
        'GoDetailsProvider: Syncing ${selectedObjects.length} objects to GoModel');

    for (final selectedObjectData in selectedObjects) {
      final object = selectedObjectData.object;
      final attributes = selectedObjectData.attributes;
      final entityId = object['entityId'] ?? object['name'] ?? 'unknown';
      final entityName =
          object['displayName'] ?? object['name'] ?? 'Unknown Entity';

      // Check if entity already exists in entitiesList
      ObjectCreationModel? existingEntity;
      try {
        existingEntity = lo.entitiesList!.firstWhere(
            (entity) => entity.id == entityId || entity.name == entityName);
      } catch (e) {
        // Entity doesn't exist, will create new one
      }

      if (existingEntity != null) {
        // Entity exists - preserve existing user-entered data
        Logger.info(
            'GoDetailsProvider: Entity "$entityName" already exists, preserving user-entered data');

        // Only add new attributes that don't already exist
        final existingAttributeNames = existingEntity.attributes
                ?.map((attr) => attr.displayName ?? attr.name)
                .toSet() ??
            <String>{};

        // Filter out attributes that already exist (attributes is List<ObjectAttribute>)
        final newAttributes = attributes
            .where((attr) =>
                !existingAttributeNames.contains(attr.displayName ?? attr.name))
            .toList();

        if (newAttributes.isNotEmpty) {
          Logger.info(
              'GoDetailsProvider: Adding ${newAttributes.length} new attributes to existing entity');

          // Combine existing and new attributes (both are ObjectAttribute objects)
          final allAttributes = <ObjectAttribute>[
            ...(existingEntity.attributes ?? <ObjectAttribute>[]),
            ...newAttributes
          ];

          // Update existing entity with combined attributes
          final updatedEntity = ObjectCreationModel(
            tenant: existingEntity.tenant,
            entityDeclaration: existingEntity.entityDeclaration,
            id: existingEntity.id,
            name: existingEntity.name,
            displayName: entityName,
            type: existingEntity.type ?? 'entity',
            description: existingEntity.description ?? 'Entity from library',
            businessPurpose: existingEntity.businessPurpose,
            businessDomain: existingEntity.businessDomain,
            category: existingEntity.category,
            archivalStrategy: existingEntity.archivalStrategy,
            colorTheme: existingEntity.colorTheme,
            icon: existingEntity.icon,
            tags: existingEntity.tags,
            attributes:
                allAttributes, // Combined attributes (preserving user data)
            relationships: existingEntity.relationships,
            businessRules: existingEntity.businessRules,
            enumValues: existingEntity.enumValues,
            securityClassification: existingEntity.securityClassification,
            systemPermissions: existingEntity.systemPermissions,
            roleSystemPermissions: existingEntity.roleSystemPermissions,
            uiProperties: existingEntity.uiProperties,
            confidence: existingEntity.confidence,
            extractionMethod: existingEntity.extractionMethod,
            completionScore: existingEntity.completionScore,
            configurationStatus: existingEntity.configurationStatus,
            createdAt: existingEntity.createdAt,
            updatedAt: DateTime.now(),
            isEntityValidatedSaved: existingEntity.isEntityValidatedSaved,
            isEntityAttributesValidatedSaved:
                existingEntity.isEntityAttributesValidatedSaved,
            isEntityRelationshipsValidatedSaved:
                existingEntity.isEntityRelationshipsValidatedSaved,
            isEntityBusinessRulesValidatedSaved:
                existingEntity.isEntityBusinessRulesValidatedSaved,
            isEntityEnumValuesValidatedSaved:
                existingEntity.isEntityEnumValuesValidatedSaved,
            isEntitySecurityClassificationValidatedSaved:
                existingEntity.isEntitySecurityClassificationValidatedSaved,
            isEntitySystemPropertiesValidatedSaved:
                existingEntity.isEntitySystemPropertiesValidatedSaved,
            attributesExpanded: existingEntity.attributesExpanded,
            relationshipsExpanded: existingEntity.relationshipsExpanded,
            enumValuesExpanded: existingEntity.enumValuesExpanded,
          );

          // Replace existing entity
          final index = lo.entitiesList!.indexWhere(
              (entity) => entity.id == entityId || entity.name == entityName);
          lo.entitiesList![index] = updatedEntity;
          Logger.info(
              'GoDetailsProvider: Updated existing entity: $entityName with ${allAttributes.length} attributes (preserved user data)');
        } else {
          Logger.info(
              'GoDetailsProvider: No new attributes to add for entity "$entityName"');
        }
      } else {
        // Create new entity with default attribute values
        Logger.info(
            'GoDetailsProvider: Creating new entity "$entityName" with default attribute values');

        // final List<ObjectAttribute> objectAttributes = [];
        // for (final attributeName in attributes) {
        //   objectAttributes.add(ObjectAttribute(
        //     name: attributeName,
        //     displayName: attributeName,
        //     dataSource: 'User', // Default data source
        //     functionType: 'String', // Default function type
        //     status: 'required',
        //     uiControl: 'String',
        //     helperText: '',
        //     errorMessage: '',
        //     dataType: 'String',
        //     required: true,
        //   ));
        // }

        final newEntity = ObjectCreationModel(
          id: entityId,
          name: entityName,
          displayName: entityName,
          type: 'entity',
          description: 'Entity from library',
          attributes: attributes,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        lo.entitiesList!.add(newEntity);
        Logger.info(
            'GoDetailsProvider: Added new entity: $entityName with ${attributes.length} attributes (default values)');
      }
    }

    Logger.info(
        'GoDetailsProvider: Completed syncing selected objects to GoModel for LO-${loIndex + 1}');
    Logger.info(
        'GoDetailsProvider: GoModel entitiesList now has ${lo.entitiesList!.length} entities');
  }

  /// Validates the current local objective
  /// Returns a map with 'clientErrors' (List<String>) and 'apiError' (String?)
  /// clientErrors: List of client-side validation errors (empty if none)
  /// apiError: API validation error message (null if successful)
  Future<Map<String, dynamic>> validateLocalObjective() async {
    if (_selectedLocalObjectiveIndex == null) {
      Logger.error(
          'GoDetailsProvider: No local objective selected for validation');
      return {
        'clientErrors': ['No local objective selected for validation'],
        'apiError': null
      };
    }

    try {
      Logger.info(
          'GoDetailsProvider: Validating local objective LO-${_selectedLocalObjectiveIndex! + 1}');

      final loIndex = _selectedLocalObjectiveIndex!;

      // Sync selected objects to GoModel before validation
      _syncSelectedObjectsToGoModel(loIndex);

      // Perform all validation checks
      final validationResults =
          await _performAllLocalObjectiveValidations(loIndex);

      // If any validation failed, return client-side error messages
      if (validationResults.isNotEmpty) {
        for (final error in validationResults) {
          Logger.error('GoDetailsProvider: $error');
        }
        return {'clientErrors': validationResults, 'apiError': null};
      }

      // All validations passed, call the API
      final apiResult = await _callLocalObjectiveValidationAPI(loIndex);

      if (apiResult == null) {
        Logger.info(
            'GoDetailsProvider: Local objective LO-${loIndex + 1} validated successfully via API');
        notifyListeners();
        return {
          'clientErrors': [],
          'apiError': null
        }; // Return empty results for success
      } else {
        Logger.error(
            'GoDetailsProvider: Local objective LO-${loIndex + 1} API validation failed');
        return {
          'clientErrors': [],
          'apiError': apiResult
        }; // Return API error message
      }
    } catch (e) {
      Logger.error('GoDetailsProvider: Error validating local objective: $e');
      return {
        'clientErrors': [],
        'apiError': 'Error validating local objective: $e'
      };
    }
  }

  /// Performs all local objective validation checks
  Future<List<String>> _performAllLocalObjectiveValidations(int loIndex) async {
    final errors = <String>[];

    // 1. Validate inputstack data (entitiesList)
    final inputstackValidation = _validateInputStackData(loIndex);
    if (inputstackValidation != null) {
      errors.add('LO-${loIndex + 1} validation failed - $inputstackValidation');
    }

    // 2. Validate function type
    final functionTypeValidation = _validateFunctionType(loIndex);
    if (functionTypeValidation != null) {
      errors
          .add('LO-${loIndex + 1} validation failed - $functionTypeValidation');
    }

    // 3. Validate role selection
    final roleValidation = _validateRoleSelection(loIndex);
    if (roleValidation != null) {
      errors.add('LO-${loIndex + 1} validation failed - $roleValidation');
    }

    // 4. Validate attributes row data
    final attributesValidation = _validateAttributesRowData(loIndex);
    if (attributesValidation != null) {
      errors.add('LO-${loIndex + 1} validation failed - $attributesValidation');
    }

    return errors;
  }

  /// Validates input stack data comprehensively - checks all user-entered attribute data
  String? _validateInputStackData(int loIndex) {
    final localObjectivesList = currentGoModel?.localObjectivesList;
    if (localObjectivesList == null || loIndex >= localObjectivesList.length) {
      return 'Local objective not found';
    }

    final localObjective = localObjectivesList[loIndex];
    final entitiesList = localObjective.entitiesList;

    if (entitiesList == null || entitiesList.isEmpty) {
      return 'No input stack data found - please add entities from library';
    }

    Logger.info(
        'GoDetailsProvider: Validating input stack data for LO-${loIndex + 1}');
    Logger.info(
        'GoDetailsProvider: Found ${entitiesList.length} entities in input stack');

    // Validate each entity in the input stack
    for (int entityIndex = 0;
        entityIndex < entitiesList.length;
        entityIndex++) {
      final entity = entitiesList[entityIndex];
      final attributes = entity.attributes;

      if (attributes == null || attributes.isEmpty) {
        return 'Entity "${entity.displayName ?? entity.name}" in input stack has no attributes defined';
      }

      Logger.info(
          'GoDetailsProvider: Validating entity "${entity.displayName ?? entity.name}" with ${attributes.length} attributes');

      // Validate each attribute in the entity
      for (int attrIndex = 0; attrIndex < attributes.length; attrIndex++) {
        final attribute = attributes[attrIndex];
        final attrName =
            attribute.displayName ?? attribute.name ?? 'Unknown Attribute';

        Logger.info('GoDetailsProvider: Validating attribute "$attrName"');

        // Comprehensive validation for each attribute field
        final validationResult =
            _validateSingleAttribute(attribute, entity, attrIndex);
        if (validationResult != null) {
          return validationResult;
        }
      }

      Logger.info(
          'GoDetailsProvider: Entity "${entity.displayName ?? entity.name}" input stack validation passed');
    }

    Logger.info(
        'GoDetailsProvider: Input stack validation completed successfully for LO-${loIndex + 1}');
    return null; // Validation passed
  }

  /// Validates a single attribute with comprehensive checks
  String? _validateSingleAttribute(
      ObjectAttribute attribute, ObjectCreationModel entity, int attrIndex) {
    final attrName =
        attribute.displayName ?? attribute.name ?? 'Unknown Attribute';
    final entityName = entity.displayName ?? entity.name ?? 'Unknown Entity';

    // 1. Basic attribute information validation
    if (attribute.name == null || attribute.name!.trim().isEmpty) {
      return 'Attribute ${attrIndex + 1} in entity "$entityName" has no name defined';
    }

    if (attribute.displayName == null ||
        attribute.displayName!.trim().isEmpty) {
      return 'Attribute "$attrName" in entity "$entityName" has no display name';
    }

    // 2. Data source validation
    if (attribute.dataSource == null || attribute.dataSource!.trim().isEmpty) {
      return 'Attribute "$attrName" in entity "$entityName" has no data source selected';
    }

    // Validate data source is one of the expected values
    final validDataSources = [
      'User',
      'Nested Function',
      'Database',
      'API',
      'File',
      'System'
    ];
    if (!validDataSources.contains(attribute.dataSource)) {
      return 'Attribute "$attrName" in entity "$entityName" has invalid data source "${attribute.dataSource}"';
    }

    // 3. Function type validation - REMOVED as per user request
    // Function type validation for attributes has been removed

    // 4. Status validation
    if (attribute.status == null || attribute.status!.trim().isEmpty) {
      return 'Attribute "$attrName" in entity "$entityName" has no status defined';
    }

    final validStatuses = ['required', 'optional'];
    if (!validStatuses.contains(attribute.status!.toLowerCase())) {
      return 'Attribute "$attrName" in entity "$entityName" has invalid status "${attribute.status}"';
    }

    // 5. UI Control validation
    if (attribute.uiControl == null || attribute.uiControl!.trim().isEmpty) {
      return 'Attribute "$attrName" in entity "$entityName" has no UI control type defined';
    }

    final validUIControls = [
      'text',
      'label',
      'email',
      'String',
      'Number',
      'Date',
      'Boolean',
      'Dropdown',
      'TextArea',
      'FileUpload',
      'Email',
      'Phone'
    ];
    if (!validUIControls.contains(attribute.uiControl)) {
      return 'Attribute "$attrName" in entity "$entityName" has invalid UI control type "${attribute.uiControl}"';
    }

    // 6. Data type validation
    if (attribute.dataType == null || attribute.dataType!.trim().isEmpty) {
      return 'Attribute "$attrName" in entity "$entityName" has no data type defined';
    }

    final validDataTypes = [
      'String',
      'Integer',
      'Float',
      'Boolean',
      'Date',
      'DateTime',
      'Array',
      'Object'
    ];
    if (!validDataTypes
        .map(
          (e) => e.toLowerCase(),
        )
        .toList()
        .contains(attribute.dataType?.toLowerCase())) {
      return 'Attribute "$attrName" in entity "$entityName" has invalid data type "${attribute.dataType}"';
    }

    // 7. Required field consistency validation
    // if (attribute.status!.toLowerCase() == 'required' &&
    //     !(attribute.required ?? false)) {
    //   return 'Attribute "$attrName" in entity "$entityName" is marked as required but required flag is not set';
    // }

    // if (attribute.status!.toLowerCase() == 'optional' &&
    //     (attribute.required ?? false)) {
    //   return 'Attribute "$attrName" in entity "$entityName" is marked as optional but required flag is set';
    // }

    // 8. Data source and function type consistency validation - REMOVED as per user request
    // Function type consistency validation for attributes has been removed

    // 9. UI Control and Data Type consistency validation
    if (attribute.uiControl == 'Number' &&
        attribute.dataType != 'Integer' &&
        attribute.dataType != 'Float') {
      return 'Attribute "$attrName" in entity "$entityName" has "Number" UI control but data type "${attribute.dataType}" is not numeric';
    }

    if (attribute.uiControl == 'Date' &&
        attribute.dataType != 'Date' &&
        attribute.dataType != 'DateTime') {
      return 'Attribute "$attrName" in entity "$entityName" has "Date" UI control but data type "${attribute.dataType}" is not date-related';
    }

    if (attribute.uiControl == 'Boolean' && attribute.dataType != 'Boolean') {
      return 'Attribute "$attrName" in entity "$entityName" has "Boolean" UI control but data type "${attribute.dataType}" is not boolean';
    }

    // 10. Helper text and error message validation (warnings only)
    if (attribute.helperText == null || attribute.helperText!.trim().isEmpty) {
      Logger.warning(
          'GoDetailsProvider: Attribute "$attrName" has no helper text (recommended for better UX)');
    }

    if (attribute.errorMessage == null ||
        attribute.errorMessage!.trim().isEmpty) {
      Logger.warning(
          'GoDetailsProvider: Attribute "$attrName" has no error message (recommended for better UX)');
    }

    Logger.info('GoDetailsProvider: Attribute "$attrName" validation passed');
    return null; // Validation passed
  }

  /// Validates attributes row data with comprehensive user-entered data validation
  String? _validateAttributesRowData(int loIndex) {
    // This method now delegates to the comprehensive input stack validation
    return _validateInputStackData(loIndex);
  }

  /// Validates function type selection
  String? _validateFunctionType(int loIndex) {
    final functionType = _loFunctionTypes[loIndex];
    if (functionType == null || functionType.trim().isEmpty) {
      return 'Function type not selected';
    }

    return null; // Validation passed
  }

  /// Validates role selection
  String? _validateRoleSelection(int loIndex) {
    final selectedRole = _loSelectedRoles[loIndex];
    final multipleRoles = _loMultipleRoles[loIndex];

    bool hasRoleSelected = false;

    // Check primary role
    if (selectedRole != null) {
      hasRoleSelected = true;
    }

    // Check multiple roles if they exist
    if (multipleRoles != null && multipleRoles.isNotEmpty) {
      for (var role in multipleRoles) {
        if (role != null) {
          hasRoleSelected = true;
          break;
        }
      }
    }

    if (!hasRoleSelected) {
      return 'No role selected';
    }

    return null; // Validation passed
  }

  /// Calls the local objective validation API
  /// Returns null on success, error message on failure
  Future<String?> _callLocalObjectiveValidationAPI(int loIndex) async {
    try {
      final localObjectivesList = currentGoModel?.localObjectivesList;
      if (localObjectivesList == null ||
          loIndex >= localObjectivesList.length) {
        Logger.error(
            'GoDetailsProvider: Local objective not found for API validation');
        return 'Local objective not found for API validation';
      }

      final localObjective = localObjectivesList[loIndex];
      final functionType = _loFunctionTypes[loIndex];
      final globalObjectives = currentGoModel?.globalObjectives;

      // Collect all selected roles
      final selectedRoles = <PostgresRole?>[];

      // Add primary role if selected
      final selectedRole = _loSelectedRoles[loIndex];
      if (selectedRole != null) {
        selectedRoles.add(selectedRole);
      }

      // Add multiple roles if they exist
      final multipleRoles = _loMultipleRoles[loIndex];
      if (multipleRoles != null && multipleRoles.isNotEmpty) {
        selectedRoles.addAll(multipleRoles.where((r) => r != null));
      }

      // Import and use the validation service
      final validationService = LocalObjectiveValidationService();
      final result = await validationService.validateLocalObjective(
        loIndex: loIndex,
        localObjective: localObjective,
        globalObjectives: globalObjectives,
        functionType: functionType,
        selectedRoles: selectedRoles,
      );

      Logger.info('GoDetailsProvider: API validation result: $result');

      // Check if the API call was successful
      if (result['success'] == true) {
        currentGoModel?.localObjectivesList?[loIndex].isValidated = true;
        Logger.info('GoDetailsProvider: LO validation and save successful');
        return null; // API call successful
      } else {
        final errorMessage = result['message'] ?? 'API validation failed';
        ValidateLoModel? errors = result['save_data'];
        Logger.error('GoDetailsProvider: API validation failed: $errorMessage');
        return errors?.validationErrors?.map((e) => e.message).join('\n') ??
            errorMessage; // API call failed
      }
    } catch (e) {
      Logger.error('GoDetailsProvider: API validation failed: $e');
      return 'API validation failed: $e'; // API call failed
    }
  }

  Future<String?> publishLo(int loIndex) async {
    try {
      final localObjectivesList = currentGoModel?.localObjectivesList;
      if (localObjectivesList == null ||
          loIndex >= localObjectivesList.length) {
        Logger.error(
            'GoDetailsProvider: Local objective not found for publishLo API validation');
        return 'Local objective not found for publishLo API validation';
      }

      final localObjective = localObjectivesList[loIndex];
      final functionType = _loFunctionTypes[loIndex];
      final globalObjectives = currentGoModel?.globalObjectives;

      // Collect all selected roles
      final selectedRoles = <PostgresRole?>[];

      // Add primary role if selected
      final selectedRole = _loSelectedRoles[loIndex];
      if (selectedRole != null) {
        selectedRoles.add(selectedRole);
      }

      // Add multiple roles if they exist
      final multipleRoles = _loMultipleRoles[loIndex];
      if (multipleRoles != null && multipleRoles.isNotEmpty) {
        selectedRoles.addAll(multipleRoles.where((r) => r != null));
      }

      // Import and use the validation service
      final validationService = LocalObjectiveValidationService();
      final result = await validationService.publishLo(
        localObjective: localObjective,
        globalObjectives: globalObjectives,
        functionType: functionType,
        selectedRoles: selectedRoles,
      );

      Logger.info('GoDetailsProvider: publishLo result: $result');

      // Check if the API call was successful
      if (result['success'] == true) {
        currentGoModel?.localObjectivesList?[loIndex].isPublished = true;
        Logger.info('GoDetailsProvider:   publishLo  successful');
        return null; // API call successful
      } else {
        final errorMessage =
            result['message']?.messages?.join('\n') ?? 'API validation failed';
        Logger.error('GoDetailsProvider: publishLo failed: $errorMessage');
        return errorMessage; // API call failed
      }
    } catch (e) {
      Logger.error('GoDetailsProvider: publishLo failed: $e');
      return 'API validation failed: $e'; // API call failed
    }
  }

  /// Cancels the current local objective details view
  void cancelLocalObjectiveDetails() {
    Logger.info('GoDetailsProvider: Canceling local objective details');
    hideLocalObjectiveDetails();
  }

  /// Sets the function type for a specific LO
  void setLoFunctionType(int loIndex, String? functionType) {
    _loFunctionTypes[loIndex] = functionType;
    Logger.info(
        'GoDetailsProvider: Set function type for LO-${loIndex + 1}: $functionType');

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    // Sync to GoModel
    syncProviderDataToGoModel();
    notifyListeners();
  }

  /// Sets the selected role for a specific LO
  void setLoSelectedRole(int loIndex, PostgresRole? role) {
    _loSelectedRoles[loIndex] = role;
    Logger.info(
        'GoDetailsProvider: Set role for LO-${loIndex + 1}: ${role?.name}');

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    // Sync to GoModel
    syncProviderDataToGoModel();
    notifyListeners();
  }

  /// Sets the execution rights for a specific LO
  void setLoExecutionRights(int loIndex, String? executionRights) {
    _loExecutionRights[loIndex] = executionRights;
    Logger.info(
        'GoDetailsProvider: Set execution rights for LO-${loIndex + 1}: $executionRights');

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    // Sync to GoModel
    syncProviderDataToGoModel();
    notifyListeners();
  }

  /// Adds a new role row for a specific LO
  void addLoRoleRow(int loIndex) {
    if (_loMultipleRoles[loIndex] == null) {
      _loMultipleRoles[loIndex] = [];
      _loMultipleExecutionRights[loIndex] = [];
    }
    _loMultipleRoles[loIndex]!.add(null);
    _loMultipleExecutionRights[loIndex]!.add(null);

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Added role row for LO-${loIndex + 1}. Total rows: ${_loMultipleRoles[loIndex]!.length}');
    notifyListeners();
  }

  /// Sets a role for a specific row in a specific LO
  void setLoMultipleRole(int loIndex, int rowIndex, PostgresRole? role) {
    if (_loMultipleRoles[loIndex] == null) {
      _loMultipleRoles[loIndex] = [];
    }
    // Ensure the list is large enough
    while (_loMultipleRoles[loIndex]!.length <= rowIndex) {
      _loMultipleRoles[loIndex]!.add(null);
    }
    _loMultipleRoles[loIndex]![rowIndex] = role;

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set role for LO-${loIndex + 1}, row ${rowIndex + 1}: ${role?.name}');
    notifyListeners();
  }

  /// Sets execution rights for a specific row in a specific LO
  void setLoMultipleExecutionRights(
      int loIndex, int rowIndex, String? executionRights) {
    if (_loMultipleExecutionRights[loIndex] == null) {
      _loMultipleExecutionRights[loIndex] = [];
    }
    // Ensure the list is large enough
    while (_loMultipleExecutionRights[loIndex]!.length <= rowIndex) {
      _loMultipleExecutionRights[loIndex]!.add(null);
    }
    _loMultipleExecutionRights[loIndex]![rowIndex] = executionRights;

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set execution rights for LO-${loIndex + 1}, row ${rowIndex + 1}: $executionRights');
    notifyListeners();
  }

  /// LO-specific accordion state setters
  void setLoSelectedObject(
      int loIndex, Map<String, dynamic> object, List<String> attributes) {
    _loSelectedObjects[loIndex] = object;
    _loSelectedObjectAttributes[loIndex] = attributes;

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set selected object for LO-${loIndex + 1}: ${object['name'] ?? object['displayName']}');
    notifyListeners();
  }

  /// Add an attribute to LO with entity and attribute existence checking
  /// Returns a result indicating the action taken
  Map<String, dynamic> addLoSelectedObjectWithCheck(int loIndex,
      Map<String, dynamic> object, List<ObjectAttribute> attributes) {
    Logger.info(
        '🔥 GoDetailsProvider.addLoSelectedObjectWithCheck called for LO-${loIndex + 1}');
    Logger.info('📦 Object: ${object['name'] ?? object['displayName']}');
    Logger.info('📋 Attributes: $attributes');

    if (_loSelectedObjectsList[loIndex] == null) {
      _loSelectedObjectsList[loIndex] = [];
      Logger.info('✨ Created new list for LO-${loIndex + 1}');
    }

    // Extract base entity ID (should now be the original entity ID without attribute suffix)
    final objectEntityId = object['entityId']?.toString() ?? '';

    // Find existing entity in the selected objects list
    int existingEntityIndex = -1;
    SelectedObjectData? existingEntity;

    for (int i = 0; i < _loSelectedObjectsList[loIndex]!.length; i++) {
      final data = _loSelectedObjectsList[loIndex]![i];
      final dataEntityId = data.object['entityId']?.toString() ?? '';

      // Check if this is the same entity by comparing entityId directly
      // Since we're no longer appending attribute suffixes to entityId, this should work correctly
      if (dataEntityId == objectEntityId && objectEntityId.isNotEmpty) {
        existingEntityIndex = i;
        existingEntity = data;
        break;
      }

      // Fallback: also check by name if entityId is not available
      if (objectEntityId.isEmpty &&
          data.object['name'] == object['name'] &&
          object['name'] != null &&
          object['name'].toString().isNotEmpty) {
        existingEntityIndex = i;
        existingEntity = data;
        break;
      }
    }

    if (existingEntity != null) {
      // Entity exists - check if attribute already exists
      final newAttribute = attributes.isNotEmpty ? attributes.first : null;
      if (newAttribute != null) {
        final newAttributeName =
            newAttribute.displayName ?? newAttribute.name ?? '';
        final existingAttributeNames = existingEntity.attributes
            .map((attr) => attr.displayName ?? attr.name ?? '')
            .toSet();

        if (existingAttributeNames.contains(newAttributeName)) {
          // Attribute already exists
          return {
            'success': false,
            'action': 'attribute_exists',
            'message':
                'Attribute "$newAttributeName" already exists in this entity'
          };
        }
      }

      // Entity exists but attribute doesn't - add attribute to existing entity
      final updatedAttributes =
          List<ObjectAttribute>.from(existingEntity.attributes);
      updatedAttributes.addAll(attributes);

      final updatedEntity = SelectedObjectData(
        object: existingEntity.object,
        attributes: updatedAttributes,
        id: existingEntity.id,
      );

      _loSelectedObjectsList[loIndex]![existingEntityIndex] = updatedEntity;

      Logger.info('📝 Added attribute to existing entity');
      Logger.info(
          'GoDetailsProvider: Added attribute to existing entity for LO-${loIndex + 1}: ${object['name'] ?? object['displayName']}');

      // Auto-expand input stack when entity or attribute is added from library
      _autoExpandInputStack();

      // Check for changes and reset published status
      // _checkForChangesAndResetPublishedStatus();

      Logger.info('🔔 Calling notifyListeners()');
      notifyListeners();

      return {
        'success': true,
        'action': 'attribute_added',
        'message': 'Added attribute to existing entity'
      };
    } else {
      // Entity doesn't exist - add new entity with attribute
      final id = SelectedObjectData.generateId(object);
      final selectedObjectData = SelectedObjectData(
        object: object,
        attributes: attributes,
        id: id,
      );
      _loSelectedObjectsList[loIndex]!.add(selectedObjectData);

      Logger.info('✨ Added new entity with attribute');
      Logger.info(
          '📊 LO-${loIndex + 1} now has ${_loSelectedObjectsList[loIndex]!.length} objects');
      Logger.info(
          'GoDetailsProvider: Added new entity for LO-${loIndex + 1}: ${object['name'] ?? object['displayName']}');

      // Auto-expand input stack when entity or attribute is added from library
      _autoExpandInputStack();

      // Check for changes and reset published status
      // _checkForChangesAndResetPublishedStatus();

      Logger.info('🔔 Calling notifyListeners()');
      notifyListeners();

      return {
        'success': true,
        'action': 'entity_added',
        'message':
            'Added new entity with attribute "${attributes.isNotEmpty ? (attributes.first.displayName ?? attributes.first.name ?? '') : ''}"'
      };
    }
  }

  void addLoSelectedObject(int loIndex, Map<String, dynamic> object,
      List<ObjectAttribute> attributes) {
    Logger.info(
        '🔥 GoDetailsProvider.addLoSelectedObject called for LO-${loIndex + 1}');
    Logger.info('📦 Object: ${object['name'] ?? object['displayName']}');
    Logger.info('📋 Attributes: $attributes');

    // Show loading state
    _setAddingEntityState(true,
        'Adding ${object['displayName'] ?? object['name']} to LO-${loIndex + 1}...');

    // Use Future.microtask to ensure UI updates before processing
    Future.microtask(() async {
      try {
        if (_loSelectedObjectsList[loIndex] == null) {
          _loSelectedObjectsList[loIndex] = [];
          Logger.info('✨ Created new list for LO-${loIndex + 1}');
        }

        final id = SelectedObjectData.generateId(object);
        final selectedObjectData = SelectedObjectData(
          object: object,
          attributes: attributes,
          id: id,
        );
        _loSelectedObjectsList[loIndex]!.add(selectedObjectData);

        Logger.info(
            '📊 LO-${loIndex + 1} now has ${_loSelectedObjectsList[loIndex]!.length} objects');
        Logger.info(
            'GoDetailsProvider: Added selected object for LO-${loIndex + 1}: ${object['name'] ?? object['displayName']}');

        // Auto-expand input stack when entity or attribute is added from library
        _autoExpandInputStack();

        // Check for changes and reset published status
        // _checkForChangesAndResetPublishedStatus();

        // Add a small delay to show progress (optional, for better UX)
        await Future.delayed(const Duration(milliseconds: 300));

        Logger.info('🔔 Calling notifyListeners()');
        notifyListeners();
        Logger.info('✅ addLoSelectedObject completed');
      } catch (e) {
        Logger.info('❌ Error in addLoSelectedObject: $e');
        Logger.error('GoDetailsProvider: Error adding selected object: $e');
      } finally {
        // Hide loading state
        _setAddingEntityState(false);
      }
    });
  }

  /// Getter for auto-expand input stack flag
  bool get shouldAutoExpandInputStack => _shouldAutoExpandInputStack;

  /// Auto-expand the input stack accordion when entities or attributes are added from library
  void _autoExpandInputStack() {
    _shouldAutoExpandInputStack = true;
    Logger.info(
        'GoDetailsProvider: Requesting auto-expand of input stack accordion');
    notifyListeners();
  }

  /// Reset auto-expand flag after it has been handled
  void resetAutoExpandInputStack() {
    _shouldAutoExpandInputStack = false;
    notifyListeners();
  }

  /// Loading state for entity addition
  bool _isAddingEntity = false;
  String _addingEntityMessage = '';

  /// Getter for entity addition loading state
  bool get isAddingEntity => _isAddingEntity;
  String get addingEntityMessage => _addingEntityMessage;

  /// Set loading state for entity addition
  void _setAddingEntityState(bool isLoading, [String message = '']) {
    _isAddingEntity = isLoading;
    _addingEntityMessage = message;
    notifyListeners();
  }

  void removeLoSelectedObject(int loIndex, String objectId) {
    if (_loSelectedObjectsList[loIndex] != null) {
      _loSelectedObjectsList[loIndex]!
          .removeWhere((data) => data.id == objectId);

      // Check for changes and reset published status
      // _checkForChangesAndResetPublishedStatus();

      Logger.info(
          'GoDetailsProvider: Removed selected object for LO-${loIndex + 1}: $objectId');
      notifyListeners();
    }
  }

  /// Remove a specific attribute from an entity in the selected objects list
  void removeLoSelectedObjectAttribute(
      int loIndex, String objectId, String attributeName) {
    Logger.info('🗑️ GoDetailsProvider.removeLoSelectedObjectAttribute called');
    Logger.info('   LO Index: $loIndex');
    Logger.info('   Object ID: $objectId');
    Logger.info('   Attribute: $attributeName');

    if (_loSelectedObjectsList[loIndex] == null) {
      Logger.info('❌ No selected objects list for LO-${loIndex + 1}');
      return;
    }

    // Find the entity
    final entityIndex = _loSelectedObjectsList[loIndex]!
        .indexWhere((data) => data.id == objectId);

    if (entityIndex == -1) {
      Logger.info('❌ Entity not found: $objectId');
      return;
    }

    final entity = _loSelectedObjectsList[loIndex]![entityIndex];
    final updatedAttributes = List<ObjectAttribute>.from(entity.attributes);

    // Remove the attribute by matching displayName or name
    final initialLength = updatedAttributes.length;
    updatedAttributes.removeWhere(
        (attr) => (attr.displayName ?? attr.name) == attributeName);

    if (updatedAttributes.length == initialLength) {
      Logger.info('❌ Attribute not found in entity: $attributeName');
      return;
    }

    Logger.info('✅ Removed attribute "$attributeName" from entity');

    if (updatedAttributes.isEmpty) {
      // If no attributes left, remove the entire entity
      _loSelectedObjectsList[loIndex]!.removeAt(entityIndex);
      Logger.info('🗑️ Removed entire entity (no attributes left)');
    } else {
      // Update the entity with remaining attributes
      final updatedEntity = SelectedObjectData(
        object: entity.object,
        attributes: updatedAttributes,
        id: entity.id,
      );
      _loSelectedObjectsList[loIndex]![entityIndex] = updatedEntity;
      Logger.info(
          '📝 Updated entity with ${updatedAttributes.length} remaining attributes');
    }

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Removed attribute "$attributeName" from entity "$objectId" for LO-${loIndex + 1}');
    notifyListeners();
  }

  void clearLoSelectedObjects(int loIndex) {
    _loSelectedObjects[loIndex] = null;
    _loSelectedObjectAttributes[loIndex] = null;
    _loSelectedObjectsList[loIndex]?.clear();

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Cleared selected objects for LO-${loIndex + 1}');
    notifyListeners();
  }

  /// Sync all provider data to the GoModel for persistence
  void syncProviderDataToGoModel() {
    if (currentGoModel?.localObjectivesList == null) {
      Logger.warning(
          'GoDetailsProvider: Cannot sync - GoModel or localObjectivesList is null');
      return;
    }

    Logger.info('GoDetailsProvider: Starting sync of provider data to GoModel');

    for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
      final lo = currentGoModel!.localObjectivesList![i];

      // Sync function type
      if (_loFunctionTypes.containsKey(i)) {
        lo.functionType = _loFunctionTypes[i];
      }

      // Sync execution rights
      if (_loExecutionRights.containsKey(i)) {
        lo.executionRights = _loExecutionRights[i];
      }

      // Sync role types (from selected roles)
      if (_loSelectedRoles.containsKey(i) && _loSelectedRoles[i] != null) {
        lo.roleTypes = [
          _loSelectedRoles[i]!.name!
        ]; // Single role for backward compatibility
      }

      Logger.info('GoDetailsProvider: Synced data for LO-${i + 1}: '
          'functionType=${lo.functionType}, executionRights=${lo.executionRights}, '
          'roleTypes=${lo.roleTypes}, entities=${lo.entitiesList?.length ?? 0}');
    }

    Logger.info(
        'GoDetailsProvider: Completed sync of provider data to GoModel');
    notifyListeners();
  }

  /// Load data from GoModel into provider state (called when provider is initialized with existing GoModel)
  void loadProviderDataFromGoModel() {
    if (currentGoModel?.localObjectivesList == null) {
      Logger.warning(
          'GoDetailsProvider: Cannot load - GoModel or localObjectivesList is null');
      return;
    }

    Logger.info('GoDetailsProvider: Loading provider data from GoModel');

    // Clear existing provider data
    _loFunctionTypes.clear();
    _loExecutionRights.clear();
    _loSelectedRoles.clear();

    for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
      final lo = currentGoModel!.localObjectivesList![i];

      // Load function type
      if (lo.functionType != null) {
        _loFunctionTypes[i] = lo.functionType;
      }

      // Load execution rights
      if (lo.executionRights != null) {
        _loExecutionRights[i] = lo.executionRights;
      }

      // Load role type (would need to convert back to PostgresRole object)
      // For now, we'll skip this as it requires role lookup

      Logger.info('GoDetailsProvider: Loaded data for LO-${i + 1}: '
          'functionType=${lo.functionType}, executionRights=${lo.executionRights}, '
          'roleType=${lo.roleTypes}, entities=${lo.entitiesList?.length ?? 0}');
    }

    Logger.info(
        'GoDetailsProvider: Completed loading provider data from GoModel');
    notifyListeners();
  }

  /// Removes a role row for a specific LO
  void removeLoRoleRow(int loIndex, int rowIndex) {
    if (_loMultipleRoles[loIndex] != null &&
        rowIndex < _loMultipleRoles[loIndex]!.length) {
      _loMultipleRoles[loIndex]!.removeAt(rowIndex);
    }
    if (_loMultipleExecutionRights[loIndex] != null &&
        rowIndex < _loMultipleExecutionRights[loIndex]!.length) {
      _loMultipleExecutionRights[loIndex]!.removeAt(rowIndex);
    }

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Removed role row ${rowIndex + 1} for LO-${loIndex + 1}');
    notifyListeners();
  }

  /// Toggles LO insertion text field for a specific LO
  void toggleLoInsertion(int index) {
    // Close pathway creation if it's open
    _isPathwayCreationOpen[index] = false;
     _isLoInsertionOpen[index] = !(_isLoInsertionOpen[index] ?? false);
    final isCurrentlyOpen = _loInsertionStates[index] ?? false;

    // Close all other insertion fields first
    _closeAllLoInsertions();

    if (!isCurrentlyOpen) {
      // Open insertion for this LO
      _loInsertionStates[index] = true;
      _loInsertionControllers[index] = TextEditingController();
    }

    Logger.info(
        'GoDetailsProvider: Toggled LO insertion for LO-${index + 1}: ${_loInsertionStates[index]}');
    notifyListeners();
  }

  /// Closes all LO insertion text fields
  void _closeAllLoInsertions() {
    // Dispose controllers and clear states
    for (final controller in _loInsertionControllers.values) {
      controller.dispose();
    }
    _loInsertionControllers.clear();
    _loInsertionStates.clear();
  }

  /// Processes and inserts new LOs after the specified index
  void processLoInsertion(int afterIndex) {
    final controller = _loInsertionControllers[afterIndex];
    if (controller == null) {
      Logger.warning(
          'GoDetailsProvider: No controller found for LO insertion at index $afterIndex');
      return;
    }

    final inputText = controller.text.trim();
    if (inputText.isEmpty) {
      Logger.warning('GoDetailsProvider: No text provided for LO insertion');
      return;
    }

    // Split by dots and clean up
    final newObjectives = inputText
        .split('.')
        .map((obj) => obj.trim())
        .where((obj) => obj.isNotEmpty)
        .toList();

    if (newObjectives.isEmpty) {
      Logger.warning('GoDetailsProvider: No valid LOs found for insertion');
      return;
    }

    // Capitalize first letter of each objective
    final formattedObjectives = newObjectives
        .map((obj) => obj.isEmpty
            ? obj
            : obj[0].toUpperCase() + obj.substring(1).toLowerCase())
        .toList();
    final newLocalObjectives = formattedObjectives
        .map((obj) => LocalObjectivesList(name: obj))
        .toList();
    // Insert new LOs after the specified index
    _insertLocalObjectives(afterIndex, newLocalObjectives);

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    // Close the insertion field
    _loInsertionStates[afterIndex] = false;
    controller.dispose();
    _loInsertionControllers.remove(afterIndex);

    Logger.info(
        'GoDetailsProvider: Inserted ${formattedObjectives.length} LOs after index $afterIndex: $formattedObjectives');
    notifyListeners();
  }

  /// Inserts new local objectives at the specified position
  void _insertLocalObjectives(
      int afterIndex, List<LocalObjectivesList> newObjectives) {
    // Insert new objectives after the specified index
    for (int i = 0; i < newObjectives.length; i++) {
      currentGoModel!.localObjectivesList!
          .insert(afterIndex + 1 + i, newObjectives[i]);
    }

    // Update LO numbers after insertion
    _updateLoNumbers();
  }

  /// Reorder LOs by moving an LO from one position to another
  void reorderLocalObjectives(int oldIndex, int newIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        oldIndex < 0 ||
        newIndex < 0 ||
        oldIndex >= currentGoModel!.localObjectivesList!.length ||
        newIndex >= currentGoModel!.localObjectivesList!.length) {
      Logger.warning('GoDetailsProvider: Invalid indices for LO reordering');
      return;
    }

    // Remove the LO from old position
    final lo = currentGoModel!.localObjectivesList!.removeAt(oldIndex);

    // Insert at new position
    currentGoModel!.localObjectivesList!.insert(newIndex, lo);

    // Update LO numbers after reordering
    _updateLoNumbers();

    Logger.info(
        'GoDetailsProvider: Reordered LO "${lo.name}" from position ${oldIndex + 1} to ${newIndex + 1}');
    notifyListeners();
  }

  /// Remove a specific LO by index
  void removeLocalObjective(int index) {
    if (currentGoModel?.localObjectivesList == null ||
        index < 0 ||
        index >= currentGoModel!.localObjectivesList!.length) {
      Logger.warning('GoDetailsProvider: Invalid index for LO removal');
      return;
    }

    final removedLo = currentGoModel!.localObjectivesList!.removeAt(index);

    // Update LO numbers after removal
    _updateLoNumbers();

    Logger.info(
        'GoDetailsProvider: Removed LO "${removedLo.name}" from position ${index + 1}');
    notifyListeners();
  }

  /// Update LO numbers to maintain sequential numbering
  void _updateLoNumbers() {
    if (currentGoModel?.localObjectivesList == null) return;

    for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
      currentGoModel!.localObjectivesList![i].loNumber = i + 1;
    }
  }

  /// Resets to initial step
  void resetToInitial() {
    _currentStep = GoDetailsStep.initial;
    _generatedDescription = null;
    // _localObjectives.clear();
    currentGoModel = null;
    descriptionController.clear();
    localObjectiveController.clear();
    _setValidationError(null);
    hideLocalObjectiveDetails();
    Logger.info('GoDetailsProvider: Reset to initial step');
    notifyListeners();
  }

  /// Clears all form data
  void clearForm() {
    solutionController.clear();
    descriptionController.clear();
    localObjectiveController.clear();
    _selectedRole = null;
    hideLocalObjectiveDetails();
    resetToInitial();
    Logger.info('GoDetailsProvider: Form cleared');
  }

  /// Private helper methods
  void _setValidating(bool validating) {
    if (_isValidating != validating) {
      _isValidating = validating;
      notifyListeners();
    }
  }

  void _setValidationError(String? error) {
    if (_validationError != error) {
      _validationError = error;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    // Remove listeners before disposing controllers
    solutionController.removeListener(_onSolutionChanged);
    descriptionController.removeListener(_onDescriptionChanged);

    solutionController.dispose();
    descriptionController.dispose();
    localObjectiveController.dispose();

    // Dispose LO insertion controllers
    for (final controller in _loInsertionControllers.values) {
      controller.dispose();
    }
    _loInsertionControllers.clear();

    super.dispose();
  }

  /// Creates GoModel from current controller values
  GoModel createGoModelFromControllers(GoDetailsProvider goDetailsProvider) {
    // Create GlobalObjectives with solution name and description
    final globalObjectives = GlobalObjectives(
      name: goDetailsProvider.solutionController.text.trim(),
      description: goDetailsProvider.descriptionController.text.trim(),
      naturalLanguage: goDetailsProvider.solutionController.text.trim(),
      roleTypes: goDetailsProvider.selectedRoles
          .map((role) => role.name ?? '')
          .where((name) => name.isNotEmpty)
          .toList(),
      version: "1.0",
      status: "Active",
      classification: "",
    );

    // Create LocalObjectivesList from LO names
    final localObjectivesList = <LocalObjectivesList>[];
    if (goDetailsProvider.currentGoModel != null) {
      for (int i = 0;
          i < goDetailsProvider.currentGoModel!.localObjectivesList!.length;
          i++) {
        final loName =
            goDetailsProvider.currentGoModel!.localObjectivesList![i];
        if ((loName.name ?? '').isNotEmpty) {
          // Create pathway data if pathway creation is open for this LO
          PathwayData? pathwayData;
          if (goDetailsProvider.isPathwayCreationOpen(i)) {
            pathwayData = _createPathwayData(i, goDetailsProvider);
          }

          final roleTypes = goDetailsProvider
              .getPathwaySelectedMultipleRoles(i)
              .map((role) => role.name ?? '')
              .where((name) => name.isNotEmpty)
              .toList();
          final isTerminal =
              goDetailsProvider.getPathwaySelectedType(i) == 'Terminal';

          localObjectivesList.add(LocalObjectivesList(
            loNumber: i + 1,
            name: loName.name,
            version: "1.0",
            status: "Active",
            naturalLanguage: loName.name,
            goId: "go_${DateTime.now().millisecondsSinceEpoch}",
            loId: "lo_${i + 1}_${DateTime.now().millisecondsSinceEpoch}",
            roleTypes: roleTypes,
            terminal: isTerminal,
            pathwayData: pathwayData,
          ));
        }
      }
    }

    // Create and return GoModel
    return GoModel(
      globalObjectives: globalObjectives,
      localObjectivesList: localObjectivesList,
    );
  }

  /// Creates PathwayData from provider data for a specific LO
  PathwayData _createPathwayData(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);
    final selectedRoles =
        goDetailsProvider.getPathwaySelectedMultipleRoles(loIndex);

    PathwayData pathwayData = PathwayData(
      selectedRoles: selectedRoles
          .map((role) => role.name ?? '')
          .where((name) => name.isNotEmpty)
          .toList(),
      selectedType: selectedType,
    );

    switch (selectedType) {
      case 'Sequential':
        final selectedLO = goDetailsProvider.getPathwaySelectedLO(loIndex);
        pathwayData.sequentialData = SequentialPathwayData(
          selectedLO: selectedLO,
        );
        break;

      case 'Alternative':
        // Get all pathway entries for this LO
        final pathwayEntries = goDetailsProvider.getPathwayEntries(loIndex);
        pathwayData.alternativeData = AlternativePathwayData(
          pathwayEntries: pathwayEntries,
        );
        break;

      case 'Parallel':
        // Get all pathway entries for this LO
        final pathwayEntries = goDetailsProvider.getPathwayEntries(loIndex);
        pathwayData.parallelData = ParallelPathwayData(
          pathwayEntries: pathwayEntries,
        );
        break;

      case 'Recursive':
        pathwayData.recursiveData = RecursivePathwayData(
          isRecursive: true,
        );
        break;

      case 'Terminal':
        pathwayData.isTerminal = true;
        break;
    }

    return pathwayData;
  }

  Future<bool> validateGO({bool isEditMode = false}) async {
    Logger.info('GoDetailsProvider: validateGO() called');
    // Add your validation logic here
    // For example, you might want to validate the current GoModel
    if (currentGoModel != null) {
      Logger.info(
          'GoDetailsProvider: Validating GO: ${currentGoModel!.globalObjectives?.name}');

      try {
        final authService = AuthService();
        final savedData = await authService.getSavedAuthData();
        final tenantId = savedData.data?.user?.tenantId ?? '';
        final tenantName = savedData.data?.user?.tenantName ?? '';
        currentGoModel = createGoModelFromControllers(this);

        String naturalLanguage =
            GoModelToTextConverter.convertToText(currentGoModel!, tenantId);
        Logger.info('🔥 GoText: $naturalLanguage');
        final result = await _entityParseValidationService.parseValidateGO(
          naturalLanguage: naturalLanguage,
        );

        parseValidateGo = result;

        if (parseValidateGo?.success ?? false) {
          final result1 =
              await _entityParseValidationService.parseValidateSaveGO(
                  naturalLanguage: naturalLanguage, isEditMode: isEditMode);
          parseValidateGo = result1;
          if (parseValidateGo?.success ?? false) {
            currentGoModel?.globalObjectives?.goId =
                parseValidateGo?.parsedData?.go?.globalObjectives?.goId;
            isGoValidateVisible = true;
            currentGoModel?.globalObjectives?.isValidated = true;
            notifyListeners();
            return false;
          } else {
            return true;
          }
        } else {
          return true; // Has error
        }
      } catch (e, s) {
        if (kDebugMode) {
          Logger.info("$e. $s");
        }
        Logger.error('GoDetailsProvider: Error validating GO: $e');
        return true; // Has error
      }
    } else {
      Logger.warning('GoDetailsProvider: No GoModel to validate');
      return true; // Has error
    }
  }

  Future<bool> publishGo({bool isEditMode = false}) async {
    Logger.info('GoDetailsProvider: publishGo() called');
    if (currentGoModel != null) {
      Logger.info(
          'GoDetailsProvider: Publishing GO: ${currentGoModel!.globalObjectives?.name}');

      try {
        final authService = AuthService();
        final savedData = await authService.getSavedAuthData();
        final tenantId = savedData.data?.user?.tenantId ?? '';
        String naturalLanguage =
            GoModelToTextConverter.convertToText(currentGoModel!, tenantId);
        Logger.info('🔥 GoText: $naturalLanguage');
        final result = await _entityParseValidationService.publishGo(
          naturalLanguage: naturalLanguage,
        );

        parseValidateGo = result;

        if (parseValidateGo?.success ?? false) {
          final result1 =
              await _entityParseValidationService.parseValidateSaveGO(
                  naturalLanguage: naturalLanguage, isEditMode: isEditMode);
          parseValidateGo = result1;
          if (parseValidateGo?.success ?? false) {
            currentGoModel?.globalObjectives?.goId =
                parseValidateGo?.parsedData?.go?.globalObjectives?.goId;
            isGoValidateVisible = true;
            currentGoModel?.globalObjectives?.isPublished = true;
            notifyListeners();
            return false; // No error
          } else {
            return true; // Has error
          }
        } else {
          return true; // Has error
        }
      } catch (e, s) {
        if (kDebugMode) {
          Logger.info("$e. $s");
        }
        Logger.error('GoDetailsProvider: Error publishing GO: $e');
        return true; // Has error
      }
    } else {
      Logger.warning('GoDetailsProvider: No GoModel to publish');
      return true; // Has error
    }
  }
}
